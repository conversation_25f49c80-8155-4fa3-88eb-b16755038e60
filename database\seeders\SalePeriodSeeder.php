<?php

namespace Database\Seeders;

use App\Models\Campaign;
use App\Models\SalePeriod;
use App\Models\SubsType;
use Illuminate\Database\Seeder;

class SalePeriodSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get all campaigns
        $campaigns = Campaign::all();
        
        // Get all subscription types
        $subsTypes = SubsType::all();
        
        // Define sale periods data
        $salePeriods = [
            // Back to School Campaign periods
            [
                'nom_fr' => 'Période de vente rentrée scolaire - Abonnement scolaire',
                'nom_en' => 'Back to School Sale Period - School Subscription',
                'nom_ar' => 'فترة بيع العودة إلى المدرسة - اشتراك مدرسي',
                'date_start' => '2025-04-25',
                'date_end' => '2025-08-25',
                'campaign_index' => 0, // Campagne Rentrée Scolaire 2025
                'subs_type_index' => 0, // Abonnement scolaire
            ],
            [
                'nom_fr' => 'Période de vente rentrée scolaire - Abonnement universitaire',
                'nom_en' => 'Back to School Sale Period - University Subscription',
                'nom_ar' => 'فترة بيع العودة إلى المدرسة - اشتراك جامعي',
                'date_start' => '2025-04-25',
                'date_end' => '2025-08-25',
                'campaign_index' => 0, // Campagne Rentrée Scolaire 2025
                'subs_type_index' => 1, // Abonnement universitaire
            ],
            
            // Summer Campaign periods
            [
                'nom_fr' => 'Période de vente été - Abonnement civil',
                'nom_en' => 'Summer Sale Period - Civil Subscription',
                'nom_ar' => 'فترة بيع الصيف - اشتراك مدني',
                'date_start' => '2025-04-25',
                'date_end' => '2025-08-25',
                'campaign_index' => 1, // Campagne Été 2025
                'subs_type_index' => 2, // Abonnement civil
            ],
            [
                'nom_fr' => 'Période de vente été - Abonnement impersonnel',
                'nom_en' => 'Summer Sale Period - Impersonal Subscription',
                'nom_ar' => 'فترة بيع الصيف - اشتراك غير شخصي',
                'date_start' => '2025-04-25',
                'date_end' => '2025-08-25',
                'campaign_index' => 1, // Campagne Été 2025
                'subs_type_index' => 3, // Abonnement impersonnel
            ],
            
            // Winter Campaign periods
            [
                'nom_fr' => 'Période de vente hiver - Abonnement civil',
                'nom_en' => 'Winter Sale Period - Civil Subscription',
                'nom_ar' => 'فترة بيع الشتاء - اشتراك مدني',
                'date_start' => '2025-04-25',
                'date_end' => '2025-08-25',
                'campaign_index' => 2, // Campagne Hiver 2025
                'subs_type_index' => 2, // Abonnement civil
            ],
            [
                'nom_fr' => 'Période de vente hiver - Abonnement conventionné',
                'nom_en' => 'Winter Sale Period - Contracted Subscription',
                'nom_ar' => 'فترة بيع الشتاء - اشتراك تعاقدي',
                'date_start' => '2025-04-25',
                'date_end' => '2025-08-25',
                'campaign_index' => 2, // Campagne Hiver 2025
                'subs_type_index' => 4, // Abonnement conventionné
            ],
            
            // Special Student Campaign periods
            [
                'nom_fr' => 'Période spéciale étudiants - Abonnement universitaire',
                'nom_en' => 'Special Student Period - University Subscription',
                'nom_ar' => 'فترة خاصة للطلاب - اشتراك جامعي',
                'date_start' => '2025-04-25',
                'date_end' => '2025-08-25',
                'campaign_index' => 3, // Campagne Spéciale Étudiants
                'subs_type_index' => 1, // Abonnement universitaire
            ],
            
            // Promotional Campaign periods (inactive)
            [
                'nom_fr' => 'Période promotionnelle - Tous abonnements',
                'nom_en' => 'Promotional Period - All Subscriptions',
                'nom_ar' => 'فترة ترويجية - جميع الاشتراكات',
                'date_start' => '2025-04-25',
                'date_end' => '2025-08-25',
                'campaign_index' => 4,
                'subs_type_index' => 0,
            ]
        ];
        
        foreach ($salePeriods as $salePeriod) {
            if (isset($campaigns[$salePeriod['campaign_index']]) && isset($subsTypes[$salePeriod['subs_type_index']])) {
                SalePeriod::create([
                    'nom_fr' => $salePeriod['nom_fr'],
                    'nom_en' => $salePeriod['nom_en'],
                    'nom_ar' => $salePeriod['nom_ar'],
                    'date_start' => $salePeriod['date_start'],
                    'date_end' => $salePeriod['date_end'],
                    'id_campaign' => $campaigns[$salePeriod['campaign_index']]->id,
                    'id_abn_type' => $subsTypes[$salePeriod['subs_type_index']]->id,
                ]);
            }
        }
    }
}

